一、 项目功能总览
1. 服务端 (Server API)
服务端是整个项目的大脑和数据中心，负责所有业务逻辑、数据处理和安全验证。
用户管理模块:
接收小程序端的微信授权登录请求，创建或更新用户账户。
管理用户的核心信息（昵称、头像、OpenID）。
管理用户的账户状态（是否激活）。
管理用户的发布额度（剩余条数）。
挂牌信息模块 (公司库):
提供信息的发布、编辑、上/下架、删除接口。
提供强大的列表查询和筛选接口（根据地区、公司类型、成立年限、税务情况等所有筛选项）。
提供单个信息的详情查询接口。
邀请与激活模块:
生成包含邀请者ID的分享参数。
记录和验证邀请关系。
在满足条件时（如新用户成功注册），自动更新邀请者的账户状态（激活）或增加其发布额度。
套餐与订单模块:
提供套餐列表接口。
创建订单接口，并与微信支付服务对接，生成支付参数。
接收微信支付的异步回调通知，验证支付结果。
在支付成功后，更新订单状态，并为用户增加相应的发布额度。
聊天模块 (高级):
通过 WebSocket 提供实时消息通讯服务。
提供群组创建/管理、群成员管理接口。
提供历史消息拉取接口。
认证与安全:
使用 JWT (JSON Web Token) 或类似机制进行用户会话管理和接口访问权限控制。
2. 管理后台 (Admin Panel)
管理后台是项目运营人员的操作平台，用于管理平台上的所有数据和用户。
数据看板 (Dashboard):
核心数据统计：总用户数、日新增用户、总挂牌信息数、今日订单数、今日收入等。
用户管理:
查看所有用户列表，可按用户名、手机号搜索。
查看用户详情，包括其发布的全部信息、订单记录、邀请记录。
手动修改用户状态（如手动激活）或发布额度。
禁用/启用用户账户。
挂牌信息管理:
审核新发布的信息（关键功能，保证内容质量）。
查看、编辑、删除平台上的任何一条挂牌信息。
管理筛选标签（如未来需要新增公司类型等）。
套餐管理:
创建、编辑、上/下架发布条数套餐（如修改价格、包含条数）。
订单管理:
查看所有订单列表，可按订单号、用户搜索。
查看订单详情及支付状态。
群组管理:
创建/解散聊天群。
设置群公告。
3. 小程序端 (Mini Program)
小程序是直接面向最终用户的客户端。
用户模块:
微信一键登录和授权。
“我的”页面：显示个人信息、剩余发布条数、我的发布、我的订单记录。
核心业务模块:
公司库: 浏览、搜索、筛选挂牌信息，查看信息详情。
发布功能: 填写表单发布新的挂牌信息，发布前检查账户状态和额度。
激活与增长模块:
未激活状态下的弹窗提示。
生成个人专属的邀请分享海报或链接。
通过分享链接进入小程序的被邀请者流程处理。
支付模块:
套餐列表页：展示所有可购买的套餐。
调用微信支付接口，拉起支付。
支付结果的友好提示。
聊天模块:
群聊页面：展示不同群组的入口。
聊天界面：实时收发消息（文本、图片、需求卡片），拉取历史消息。
系统消息展示（如 “xxx邀请xxx加入群聊”）。

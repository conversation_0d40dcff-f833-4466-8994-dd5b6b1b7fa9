# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.tmp
*.temp

# IDE 和编辑器
.vscode/
!.vscode/extensions.json
!.vscode/settings.json
.idea/
*.swp
*.swo
*~
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 日志文件
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Node.js 相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.npm
.yarn-integrity
.pnpm-debug.log*

# 构建输出
dist/
dist-ssr/
build/
out/
coverage/
*.tsbuildinfo

# 环境变量和配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local
config.local.js
config.local.json

# 缓存目录
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist
.serverless/
.fusebox/

# 测试相关
coverage/
.nyc_output
.jest/
/cypress/videos/
/cypress/screenshots/

# 数据库
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 锁文件 (根据项目需要可以选择性忽略)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# uni-app 相关
unpackage/
.hbuilderx/

# 其他
*.local
.eslintcache
.stylelintcache

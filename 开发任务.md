阶段一：核心功能 MVP (最小可行产品)
目标：完成基础框架搭建，实现用户登录、信息浏览和信息发布的核心闭环。
1. 服务端 (Server-side) 开发任务
任务 1.1：项目与数据库初始化 (预计 0.5 天)
搭建 Node.js (Express/Koa) 或 Java (Spring Boot) 项目框架。
配置环境变量管理（.env 文件），用于存储数据库凭证、JWT密钥等敏感信息。
引入数据库 ORM 框架 (如 Sequelize, TypeORM, Mybatis-Plus)。
执行我们之前设计的 SQL 脚本，创建 users 和 listings 核心表。
编写 users 和 listings 表对应的 Model/Entity 文件。
任务 1.2：用户认证模块开发 (预计 1.5 天)
接口 1: 微信静默登录 (POST /api/v1/auth/login)
输入: 小程序端通过 wx.login() 获取的 code。
逻辑: 调用微信官方 code2Session 接口，用 code 换取用户的 openid 和 session_key。
数据库: 根据 openid 查询 users 表。若用户不存在，则创建新用户；若存在，则更新最后登录时间。
输出: 生成 JWT (JSON Web Token)，将 user_id, openid 等信息加密后返回给小程序端。
接口 2: 更新用户信息 (PUT /api/v1/users/me)
输入: 用户昵称 nickname、头像 avatar_url。
逻辑: 创建一个 JWT 认证中间件，用于保护需要登录才能访问的接口。此接口需使用该中间件。从 JWT 中解析出 user_id，并更新 users 表中对应的数据。
输出: 更新后的用户信息。
接口 3: 获取当前用户信息 (GET /api/v1/users/me)
逻辑: 使用 JWT 中间件，解析 user_id，查询并返回用户的完整信息（包括昵称、头像、剩余发布条数、激活状态等）。
任务 1.3：挂牌信息 (公司库) 模块开发 (预计 2 天)
接口 4: 发布新信息 (POST /api/v1/listings)
逻辑: 使用 JWT 中间件。接收小程序表单提交的所有公司信息。校验数据合法性。
数据库: 在 listings 表中插入一条新数据，user_id 从 JWT 中获取。
输出: 成功创建的消息。
接口 5: 获取信息列表 (GET /api/v1/listings)
逻辑: 无需登录即可访问。实现分页功能 (输入 page, pageSize)。按创建时间降序排序。
输出: 分页后的信息列表及总条数。
接口 6: 获取信息详情 (GET /api/v1/listings/:id)
逻辑: 根据传入的 id，查询 listings 表，并关联查询发布者的部分信息（如昵称）。
输出: 完整的单条信息详情。
接口 7: 获取我发布的信息 (GET /api/v1/users/me/listings)
逻辑: 使用 JWT 中间件，查询当前用户发布的所有信息，支持分页。
输出: 列表数据。
2. 管理后台 (Admin Panel) 开发任务
任务 2.1：项目初始化与布局 (预计 1 天)
使用 Create-React-App 或 Vue-CLI 初始化项目。
集成 UI 框架 (Ant Design 或 Element Plus)。
配置路由 (React Router / Vue Router)。
搭建后台主布局（包含顶部导航栏、侧边栏菜单、主内容区）。
封装 Axios 或 Fetch，统一处理请求、响应和 Token 注入。
任务 2.2：后台登录与用户管理 (预计 1.5 天)
创建后台登录页面 (为后台管理员创建独立的账号密码，不使用微信登录)。
实现后台登录逻辑，登录成功后将 Token 存入 localStorage。
创建用户管理页面，通过表格展示所有小程序用户。
实现用户搜索功能（按昵称或手机号）。
实现查看用户详情的弹窗或页面。
任务 2.3：挂牌信息管理 (预计 1.5 天)
创建挂牌信息管理页面，表格展示所有信息。
实现信息搜索功能（按公司名）。
实现信息的审核操作（本质是修改信息状态，或提供一个审核状态字段）。
实现信息的删除（硬删除或软删除）功能。
实现信息详情查看功能。
3. 小程序端 (Mini Program) 开发任务
任务 3.1：项目初始化与全局配置 (预计 0.5 天)
创建小程序项目，配置 app.json (页面路径、窗口表现等)。
封装全局 API 请求模块 (utils/request.js)，统一处理 Token 和服务器地址。
实现自动登录逻辑：在 onLaunch 中调用 wx.login()，获取 Token 并存入全局 globalData 或 Storage。
任务 3.2：核心页面开发 (预计 3 天)
首页/公司库页面:
在 onLoad 中请求信息列表接口 (GET /api/v1/listings)。
实现列表渲染，包括公司名、地区、成立时间等关键信息。
实现上拉加载更多（分页）。
实现点击列表项跳转到详情页。
信息详情页面:
在 onLoad 中获取 id，请求详情接口 (GET /api/v1/listings/:id)。
渲染所有详细信息。
发布页面:
创建表单，包含所有必要的输入项（输入框、选择器、日期选择器等）。
实现表单验证。
点击提交按钮时，调用发布接口 (POST /api/v1/listings)，成功后提示用户并返回上一页。
“我的”页面:
在 onShow 中请求用户信息接口 (GET /api/v1/users/me)，显示昵称、头像。
提供按钮让用户授权获取昵称头像。
创建“我的发布”入口，点击后跳转到新页面。
我的发布页面:
请求我发布的信息接口 (GET /api/v1/users/me/listings) 并渲染列表。
阶段二：激活、付费与增长闭环
目标：上线用户增长和商业化核心功能。
1. 服务端 开发任务
任务 4.1：数据库与模块扩展 (预计 0.5 天)
执行 SQL 脚本，创建 packages, orders, invitations 表。
创建对应的 Model/Entity 文件。
任务 4.2：邀请与激活模块 (预计 1 天)
逻辑完善: 修改用户登录/注册逻辑，若 URL 参数中携带 inviterId，则在 invitations 表创建记录，并更新新用户的 inviter_id 字段。
激活服务: 创建一个服务或触发器，当 invitations 表有新记录时，检查邀请者是否满足激活条件，若满足则更新其 users.status 为 active。
任务 4.3：支付模块 (预计 2.5 天)
接口 8: 获取套餐列表 (GET /api/v1/packages): 查询 packages 表中所有上架的套餐。
接口 9: 创建订单 (POST /api/v1/orders):
输入: package_id。
逻辑: 创建一条 orders 记录，状态为 pending。集成微信支付 SDK，调用统一下单接口，生成支付参数返回给小程序。
接口 10: 微信支付回调 (POST /api/v1/payments/wechat/notify):
逻辑: 这是给微信服务器调用的接口。接收并验证微信的异步通知。
数据库: 若支付成功，根据订单号更新 orders 表状态为 paid。然后，根据订单中的 user_id 和 package_id，为用户的 publishing_credits 增加相应的条数。
任务 4.4：功能校验增强 (预计 0.5 天)
修改发布接口 (POST /api/v1/listings) 的逻辑，在插入数据前，必须检查用户的 status 是否为 active 且 publishing_credits 是否大于 0。若否则返回错误。成功发布后，将 publishing_credits 减 1。
2. 管理后台 开发任务
任务 5.1：管理功能增强 (预计 2 天)
用户管理: 表格中增加“激活状态”和“剩余条数”列。在详情中允许手动修改这两个值。
套餐管理: 创建完整的 CRUD 页面，用于上架/下架、修改套餐价格和包含条数。
订单管理: 创建页面，展示所有订单列表，包括用户信息、购买的套餐、金额、支付状态。提供搜索功能。
3. 小程序端 开发任务
任务 6.1：激活与分享流程 (预计 1.5 天)
激活拦截: 在发布页面 onLoad 时，检查用户信息，若未激活，则弹窗提示（如“系统提示：您的账号未激活”）。
分享功能: 在“我的”页面或弹窗中，添加入口，调用 onShareAppMessage，在分享路径中动态加入当前用户的 ID 作为 inviterId。
任务 6.2：支付流程 (预计 2 天)
套餐页面: 请求并展示套餐列表 (GET /api/v1/packages)。
支付功能: 点击“立即领取/购买”按钮，调用创建订单接口 (POST /api/v1/orders) 获取支付参数。
调用支付: 使用 wx.requestPayment API 拉起微信支付。
结果处理: 根据支付结果给用户友好提示。
“我的”页面更新: 实时显示剩余发布条数。
阶段三：社区与体验优化 (后续迭代)
服务端:
搭建 WebSocket 服务，实现实时通讯接口。
开发聊天相关的 RESTful API (获取群信息、历史消息等)。
完善挂牌信息列表接口，支持所有筛选条件的组合查询。
管理后台:
开发数据看板 (Dashboard)，使用图表展示核心运营数据。
开发群组管理功能。
小程序:
实现完整的筛选面板 UI 和交互逻辑。
开发聊天群组和聊天界面。
进行全面的性能和体验优化。

/**
 * 数据验证工具类
 */
class Validator {
  /**
   * 验证必填字段
   * @param {Object} data 要验证的数据
   * @param {Array} requiredFields 必填字段数组
   * @returns {Array} 错误信息数组
   */
  static validateRequired(data, requiredFields) {
    const errors = [];
    
    requiredFields.forEach(field => {
      if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
        errors.push(`${field} 是必填字段`);
      }
    });
    
    return errors;
  }

  /**
   * 验证字符串长度
   * @param {string} value 要验证的值
   * @param {number} min 最小长度
   * @param {number} max 最大长度
   * @param {string} fieldName 字段名
   * @returns {string|null} 错误信息或null
   */
  static validateLength(value, min, max, fieldName) {
    if (typeof value !== 'string') {
      return `${fieldName} 必须是字符串`;
    }
    
    if (value.length < min) {
      return `${fieldName} 长度不能少于 ${min} 个字符`;
    }
    
    if (value.length > max) {
      return `${fieldName} 长度不能超过 ${max} 个字符`;
    }
    
    return null;
  }

  /**
   * 验证枚举值
   * @param {*} value 要验证的值
   * @param {Array} allowedValues 允许的值数组
   * @param {string} fieldName 字段名
   * @returns {string|null} 错误信息或null
   */
  static validateEnum(value, allowedValues, fieldName) {
    if (!allowedValues.includes(value)) {
      return `${fieldName} 必须是以下值之一: ${allowedValues.join(', ')}`;
    }
    return null;
  }

  /**
   * 验证数字范围
   * @param {number} value 要验证的值
   * @param {number} min 最小值
   * @param {number} max 最大值
   * @param {string} fieldName 字段名
   * @returns {string|null} 错误信息或null
   */
  static validateNumberRange(value, min, max, fieldName) {
    if (typeof value !== 'number' || isNaN(value)) {
      return `${fieldName} 必须是有效数字`;
    }
    
    if (value < min) {
      return `${fieldName} 不能小于 ${min}`;
    }
    
    if (value > max) {
      return `${fieldName} 不能大于 ${max}`;
    }
    
    return null;
  }

  /**
   * 验证日期格式
   * @param {string} value 要验证的日期字符串
   * @param {string} fieldName 字段名
   * @returns {string|null} 错误信息或null
   */
  static validateDate(value, fieldName) {
    if (!value) return null; // 允许空值
    
    const date = new Date(value);
    if (isNaN(date.getTime())) {
      return `${fieldName} 必须是有效的日期格式`;
    }
    
    return null;
  }

  /**
   * 验证手机号格式
   * @param {string} phone 手机号
   * @returns {boolean} 是否有效
   */
  static validatePhone(phone) {
    if (!phone) return false;
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 验证邮箱格式
   * @param {string} email 邮箱
   * @returns {boolean} 是否有效
   */
  static validateEmail(email) {
    if (!email) return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 验证URL格式
   * @param {string} url URL
   * @returns {boolean} 是否有效
   */
  static validateURL(url) {
    if (!url) return false;
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 验证挂牌信息数据
   * @param {Object} data 挂牌信息数据
   * @param {boolean} isUpdate 是否为更新操作
   * @returns {Array} 错误信息数组
   */
  static validateListingData(data, isUpdate = false) {
    const errors = [];

    // 必填字段验证（仅在创建时检查）
    if (!isUpdate) {
      const requiredFields = ['listing_type', 'company_name'];
      errors.push(...this.validateRequired(data, requiredFields));
    }
    
    // 枚举值验证
    if (data.listing_type) {
      const listingTypeError = this.validateEnum(
        data.listing_type,
        ['公司', '个体户', '代账户'],
        'listing_type'
      );
      if (listingTypeError) errors.push(listingTypeError);
    }
    
    if (data.status) {
      const statusError = this.validateEnum(
        data.status,
        ['在售', '已售', '下架'],
        'status'
      );
      if (statusError) errors.push(statusError);
    }
    
    // 字符串长度验证
    if (data.company_name) {
      const nameError = this.validateLength(data.company_name, 1, 255, 'company_name');
      if (nameError) errors.push(nameError);
    }
    
    if (data.description) {
      const descError = this.validateLength(data.description, 0, 1000, 'description');
      if (descError) errors.push(descError);
    }
    
    // 价格验证
    if (data.price !== undefined && data.price !== null) {
      if (typeof data.price !== 'number' || data.price < 0) {
        errors.push('price 必须是非负数');
      }
    }
    
    // 日期验证
    if (data.establishment_date) {
      const dateError = this.validateDate(data.establishment_date, 'establishment_date');
      if (dateError) errors.push(dateError);
    }
    
    if (data.expires_at) {
      const expiresError = this.validateDate(data.expires_at, 'expires_at');
      if (expiresError) errors.push(expiresError);
    }
    
    return errors;
  }

  /**
   * 验证用户数据
   * @param {Object} data 用户数据
   * @returns {Array} 错误信息数组
   */
  static validateUserData(data) {
    const errors = [];
    
    // 昵称验证
    if (data.nickname) {
      const nicknameError = this.validateLength(data.nickname, 1, 50, 'nickname');
      if (nicknameError) errors.push(nicknameError);
    }
    
    // 头像URL验证
    if (data.avatar_url && !this.validateURL(data.avatar_url)) {
      errors.push('avatar_url 必须是有效的URL');
    }
    
    // 手机号验证
    if (data.phone_number && !this.validatePhone(data.phone_number)) {
      errors.push('phone_number 必须是有效的手机号');
    }
    
    return errors;
  }

  /**
   * 验证分页参数
   * @param {Object} query 查询参数
   * @returns {Object} 验证后的分页参数
   */
  static validatePagination(query) {
    let { page = 1, pageSize = 10 } = query;
    
    page = parseInt(page);
    pageSize = parseInt(pageSize);
    
    // 确保参数在合理范围内
    page = Math.max(1, page);
    pageSize = Math.min(Math.max(1, pageSize), 100); // 最大100条
    
    return { page, pageSize };
  }
}

module.exports = Validator;

const axios = require('axios');

// 配置基础URL
const BASE_URL = 'http://localhost:3000/api/v1';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

/**
 * API测试脚本
 */
class APITester {
  constructor() {
    this.token = null;
  }

  /**
   * 测试健康检查
   */
  async testHealth() {
    try {
      console.log('🔍 测试健康检查...');
      const response = await axios.get('http://localhost:3000/health');
      console.log('✅ 健康检查通过:', response.data);
      return true;
    } catch (error) {
      console.error('❌ 健康检查失败:', error.message);
      return false;
    }
  }

  /**
   * 测试微信登录（模拟）
   */
  async testLogin() {
    try {
      console.log('🔍 测试微信登录...');
      
      // 注意：这里使用模拟的code，实际环境需要真实的微信code
      const response = await api.post('/auth/login', {
        code: 'mock_wechat_code_123'
      });
      
      if (response.data.success) {
        this.token = response.data.data.token;
        console.log('✅ 登录成功，获得token');
        return true;
      } else {
        console.log('⚠️ 登录失败（预期行为，因为使用了模拟code）');
        return false;
      }
    } catch (error) {
      console.log('⚠️ 登录失败（预期行为，因为使用了模拟code）:', error.response?.data?.message || error.message);
      return false;
    }
  }

  /**
   * 测试获取信息列表
   */
  async testGetListings() {
    try {
      console.log('🔍 测试获取信息列表...');
      const response = await api.get('/listings?page=1&pageSize=10');

      if (response.data.success) {
        console.log('✅ 获取信息列表成功:', {
          total: response.data.pagination.total,
          count: response.data.data.length
        });
        return true;
      } else {
        console.error('❌ 获取信息列表失败:', response.data.message);
        console.error('详细错误:', response.data);
        return false;
      }
    } catch (error) {
      console.error('❌ 获取信息列表失败:', error.response?.data?.message || error.message);
      if (error.response?.data) {
        console.error('详细错误:', error.response.data);
      }
      return false;
    }
  }

  /**
   * 测试获取信息详情
   */
  async testGetListingDetail() {
    try {
      console.log('🔍 测试获取信息详情...');
      const response = await api.get('/listings/1');
      
      if (response.data.success) {
        console.log('✅ 获取信息详情成功:', response.data.data.company_name);
        return true;
      } else {
        console.error('❌ 获取信息详情失败:', response.data.message);
        return false;
      }
    } catch (error) {
      console.error('❌ 获取信息详情失败:', error.response?.data?.message || error.message);
      return false;
    }
  }

  /**
   * 测试用户信息（需要token）
   */
  async testGetUserInfo() {
    if (!this.token) {
      console.log('⚠️ 跳过用户信息测试（无token）');
      return false;
    }

    try {
      console.log('🔍 测试获取用户信息...');
      const response = await api.get('/users/me', {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });
      
      if (response.data.success) {
        console.log('✅ 获取用户信息成功:', response.data.data.nickname || '未设置昵称');
        return true;
      } else {
        console.error('❌ 获取用户信息失败:', response.data.message);
        return false;
      }
    } catch (error) {
      console.error('❌ 获取用户信息失败:', error.response?.data?.message || error.message);
      return false;
    }
  }

  /**
   * 测试发布信息（需要token和激活状态）
   */
  async testCreateListing() {
    if (!this.token) {
      console.log('⚠️ 跳过发布信息测试（无token）');
      return false;
    }

    try {
      console.log('🔍 测试发布信息...');
      const listingData = {
        listing_type: '公司',
        company_name: '测试科技有限公司',
        price: 30000,
        is_negotiable: 1,
        registration_province: '广东省',
        registration_city: '深圳市',
        establishment_date: '2022-01-01',
        registered_capital_range: '100-500万',
        paid_in_status: '已实缴',
        company_type: '普通公司',
        tax_status: '一般纳税人',
        bank_account_status: '已开户',
        description: '这是一个测试发布的公司信息'
      };

      const response = await api.post('/listings', listingData, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });
      
      if (response.data.success) {
        console.log('✅ 发布信息成功');
        return true;
      } else {
        console.log('⚠️ 发布信息失败（可能是权限问题）:', response.data.message);
        return false;
      }
    } catch (error) {
      console.log('⚠️ 发布信息失败（可能是权限问题）:', error.response?.data?.message || error.message);
      return false;
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始API测试...\n');

    const tests = [
      { name: '健康检查', fn: () => this.testHealth() },
      { name: '微信登录', fn: () => this.testLogin() },
      { name: '获取信息列表', fn: () => this.testGetListings() },
      { name: '获取信息详情', fn: () => this.testGetListingDetail() },
      { name: '获取用户信息', fn: () => this.testGetUserInfo() },
      { name: '发布信息', fn: () => this.testCreateListing() }
    ];

    let passed = 0;
    let total = tests.length;

    for (const test of tests) {
      try {
        const result = await test.fn();
        if (result) passed++;
      } catch (error) {
        console.error(`❌ ${test.name} 测试异常:`, error.message);
      }
      console.log(''); // 空行分隔
    }

    console.log(`📊 测试结果: ${passed}/${total} 通过`);
    
    if (passed === total) {
      console.log('🎉 所有测试通过！');
    } else {
      console.log('⚠️ 部分测试失败，请检查服务器状态和配置');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const tester = new APITester();
  tester.runAllTests()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 测试运行失败:', error);
      process.exit(1);
    });
}

module.exports = APITester;

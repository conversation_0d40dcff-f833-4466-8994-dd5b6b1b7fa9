const { query } = require('../config/db');

class Listing {
  constructor(data) {
    this.id = data.id;
    this.user_id = data.user_id;
    this.listing_type = data.listing_type;
    this.company_name = data.company_name;
    this.status = data.status;
    this.price = data.price;
    this.is_negotiable = data.is_negotiable;
    this.registration_province = data.registration_province;
    this.registration_city = data.registration_city;
    this.establishment_date = data.establishment_date;
    this.registered_capital_range = data.registered_capital_range;
    this.paid_in_status = data.paid_in_status;
    this.company_type = data.company_type;
    this.tax_status = data.tax_status;
    this.bank_account_status = data.bank_account_status;
    this.has_trademark = data.has_trademark;
    this.has_patent = data.has_patent;
    this.has_software_copyright = data.has_software_copyright;
    this.has_license_plate = data.has_license_plate;
    this.has_social_security = data.has_social_security;
    this.shareholder_background = data.shareholder_background;
    this.has_bidding_history = data.has_bidding_history;
    this.description = data.description;
    this.expires_at = data.expires_at;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
    
    // 关联的用户信息（如果有）
    this.publisher_nickname = data.publisher_nickname;
  }

  /**
   * 创建新的挂牌信息
   * @param {Object} listingData 挂牌信息数据
   * @returns {Promise<Listing>}
   */
  static async create(listingData) {
    const sql = `
      INSERT INTO listings (
        user_id, listing_type, company_name, status, price, is_negotiable,
        registration_province, registration_city, establishment_date,
        registered_capital_range, paid_in_status, company_type, tax_status,
        bank_account_status, has_trademark, has_patent, has_software_copyright,
        has_license_plate, has_social_security, shareholder_background,
        has_bidding_history, description, expires_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      listingData.user_id,
      listingData.listing_type,
      listingData.company_name,
      listingData.status || '在售',
      listingData.price || null,
      listingData.is_negotiable || 0,
      listingData.registration_province || null,
      listingData.registration_city || null,
      listingData.establishment_date || null,
      listingData.registered_capital_range || null,
      listingData.paid_in_status || null,
      listingData.company_type || null,
      listingData.tax_status || null,
      listingData.bank_account_status || null,
      listingData.has_trademark || 0,
      listingData.has_patent || 0,
      listingData.has_software_copyright || 0,
      listingData.has_license_plate || 0,
      listingData.has_social_security || 0,
      listingData.shareholder_background || null,
      listingData.has_bidding_history || 0,
      listingData.description || null,
      listingData.expires_at || null
    ];
    
    const result = await query(sql, params);
    return await Listing.findById(result.insertId);
  }

  /**
   * 根据ID查找挂牌信息
   * @param {number} id 挂牌信息ID
   * @returns {Promise<Listing|null>}
   */
  static async findById(id) {
    const sql = `
      SELECT l.*, u.nickname as publisher_nickname
      FROM listings l
      LEFT JOIN users u ON l.user_id = u.id
      WHERE l.id = ?
    `;
    const rows = await query(sql, [id]);
    return rows.length > 0 ? new Listing(rows[0]) : null;
  }

  /**
   * 获取挂牌信息列表（分页）
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  static async getList(options = {}) {
    const {
      page = 1,
      pageSize = 10,
      status = '在售',
      listing_type,
      registration_city,
      company_type,
      tax_status,
      search
    } = options;

    // 确保page和pageSize是数字类型
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    
    // 构建WHERE条件
    const conditions = ['l.status = ?'];
    const params = [status];
    
    if (listing_type) {
      conditions.push('l.listing_type = ?');
      params.push(listing_type);
    }
    
    if (registration_city) {
      conditions.push('l.registration_city = ?');
      params.push(registration_city);
    }
    
    if (company_type) {
      conditions.push('l.company_type = ?');
      params.push(company_type);
    }
    
    if (tax_status) {
      conditions.push('l.tax_status = ?');
      params.push(tax_status);
    }
    
    if (search) {
      conditions.push('l.company_name LIKE ?');
      params.push(`%${search}%`);
    }
    
    const whereClause = `WHERE ${conditions.join(' AND ')}`;

    // 查询总数
    const countSql = `
      SELECT COUNT(*) as total
      FROM listings l
      ${whereClause}
    `;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;

    // 查询列表数据
    const listSql = `
      SELECT l.*, u.nickname as publisher_nickname
      FROM listings l
      LEFT JOIN users u ON l.user_id = u.id
      ${whereClause}
      ORDER BY l.created_at DESC
      LIMIT ${pageSizeNum} OFFSET ${offset}
    `;

    const rows = await query(listSql, params);

    return {
      data: rows.map(row => new Listing(row)),
      pagination: {
        page: pageNum,
        pageSize: pageSizeNum,
        total,
        totalPages: Math.ceil(total / pageSizeNum)
      }
    };
  }

  /**
   * 获取用户发布的挂牌信息
   * @param {number} userId 用户ID
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  static async getUserListings(userId, options = {}) {
    const { page = 1, pageSize = 10 } = options;

    // 确保page和pageSize是数字类型
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;

    // 查询总数
    const countSql = 'SELECT COUNT(*) as total FROM listings WHERE user_id = ?';
    const countResult = await query(countSql, [userId]);
    const total = countResult[0].total;

    // 查询列表数据
    const listSql = `
      SELECT l.*, u.nickname as publisher_nickname
      FROM listings l
      LEFT JOIN users u ON l.user_id = u.id
      WHERE l.user_id = ?
      ORDER BY l.created_at DESC
      LIMIT ${pageSizeNum} OFFSET ${offset}
    `;

    const rows = await query(listSql, [userId]);

    return {
      data: rows.map(row => new Listing(row)),
      pagination: {
        page: pageNum,
        pageSize: pageSizeNum,
        total,
        totalPages: Math.ceil(total / pageSizeNum)
      }
    };
  }

  /**
   * 更新挂牌信息
   * @param {number} id 挂牌信息ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<Listing>}
   */
  static async update(id, updateData) {
    const fields = [];
    const values = [];
    
    // 动态构建更新字段
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });
    
    if (fields.length === 0) {
      throw new Error('没有要更新的字段');
    }
    
    values.push(id);
    const sql = `UPDATE listings SET ${fields.join(', ')} WHERE id = ?`;
    
    await query(sql, values);
    return await Listing.findById(id);
  }

  /**
   * 删除挂牌信息
   * @param {number} id 挂牌信息ID
   * @returns {Promise<boolean>}
   */
  static async delete(id) {
    const sql = 'DELETE FROM listings WHERE id = ?';
    const result = await query(sql, [id]);
    return result.affectedRows > 0;
  }

  /**
   * 获取挂牌信息统计
   * @returns {Promise<Object>}
   */
  static async getStats() {
    const totalSql = 'SELECT COUNT(*) as total FROM listings';
    const activeSql = 'SELECT COUNT(*) as active FROM listings WHERE status = "在售"';
    const todaySql = 'SELECT COUNT(*) as today FROM listings WHERE DATE(created_at) = CURDATE()';
    
    const [totalResult, activeResult, todayResult] = await Promise.all([
      query(totalSql),
      query(activeSql),
      query(todaySql)
    ]);
    
    return {
      total: totalResult[0].total,
      active: activeResult[0].active,
      today: todayResult[0].today
    };
  }

  /**
   * 转换为JSON对象
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id,
      user_id: this.user_id,
      listing_type: this.listing_type,
      company_name: this.company_name,
      status: this.status,
      price: this.price,
      is_negotiable: this.is_negotiable,
      registration_province: this.registration_province,
      registration_city: this.registration_city,
      establishment_date: this.establishment_date,
      registered_capital_range: this.registered_capital_range,
      paid_in_status: this.paid_in_status,
      company_type: this.company_type,
      tax_status: this.tax_status,
      bank_account_status: this.bank_account_status,
      has_trademark: this.has_trademark,
      has_patent: this.has_patent,
      has_software_copyright: this.has_software_copyright,
      has_license_plate: this.has_license_plate,
      has_social_security: this.has_social_security,
      shareholder_background: this.shareholder_background,
      has_bidding_history: this.has_bidding_history,
      description: this.description,
      expires_at: this.expires_at,
      created_at: this.created_at,
      updated_at: this.updated_at,
      publisher_nickname: this.publisher_nickname
    };
  }
}

module.exports = Listing;

{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/segmented/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Segmented from './src/segmented.vue'\n\nexport const ElSegmented = withInstall(Segmented)\nexport default ElSegmented\n\nexport * from './src/segmented'\n"], "names": ["withInstall", "Segmented"], "mappings": ";;;;;;;;AAEY,MAAC,WAAW,GAAGA,mBAAW,CAACC,sBAAS;;;;;;;;"}
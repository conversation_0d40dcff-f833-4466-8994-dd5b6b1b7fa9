{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/form/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON>oopInstall } from '@element-plus/utils'\nimport Form from './src/form.vue'\nimport FormItem from './src/form-item.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElForm: SFCWithInstall<typeof Form> & {\n  FormItem: typeof FormItem\n} = withInstall(Form, {\n  FormItem,\n})\nexport default ElForm\nexport const ElFormItem: SFCWithInstall<typeof FormItem> =\n  withNoopInstall(FormItem)\n\nexport * from './src/form'\nexport * from './src/form-item'\nexport * from './src/types'\nexport * from './src/constants'\nexport * from './src/hooks'\n\nexport type FormInstance = InstanceType<typeof Form> & unknown\nexport type FormItemInstance = InstanceType<typeof FormItem> & unknown\n"], "names": ["withInstall", "Form", "FormItem", "withNoopInstall"], "mappings": ";;;;;;;;;;;;;AAGY,MAAC,MAAM,GAAGA,mBAAW,CAACC,iBAAI,EAAE;AACxC,YAAEC,qBAAQ;AACV,CAAC,EAAE;AAES,MAAC,UAAU,GAAGC,uBAAe,CAACD,qBAAQ;;;;;;;;;;;;;;;;;;;"}
{"version": 3, "file": "constants.js", "sources": ["../../../../../../packages/components/form/src/constants.ts"], "sourcesContent": ["import type { InjectionKey } from 'vue'\nimport type { FormContext, FormItemContext } from './types'\n\nexport const formContextKey: InjectionKey<FormContext> =\n  Symbol('formContextKey')\nexport const formItemContextKey: InjectionKey<FormItemContext> =\n  Symbol('formItemContextKey')\n"], "names": [], "mappings": ";;;;AAAY,MAAC,cAAc,GAAG,MAAM,CAAC,gBAAgB,EAAE;AAC3C,MAAC,kBAAkB,GAAG,MAAM,CAAC,oBAAoB;;;;;"}
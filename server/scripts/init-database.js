const fs = require('fs');
const path = require('path');
const { pool } = require('../config/db');

/**
 * 数据库初始化脚本
 * 执行SQL文件创建数据库表结构
 */
async function initDatabase() {
  try {
    console.log('🚀 开始初始化数据库...');

    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, '../../sql.sql');
    
    if (!fs.existsSync(sqlFilePath)) {
      console.error('❌ SQL文件不存在:', sqlFilePath);
      process.exit(1);
    }

    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // 分割SQL语句（以分号分割，忽略注释）
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📄 找到 ${statements.length} 条SQL语句`);

    // 获取数据库连接
    const connection = await pool.getConnection();

    try {
      // 执行每条SQL语句
      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];

        if (statement.trim()) {
          console.log(`⚡ 执行第 ${i + 1} 条语句...`);
          try {
            await connection.execute(statement);
          } catch (error) {
            // 如果是表已存在的错误，跳过
            if (error.code === 'ER_TABLE_EXISTS_ERROR') {
              console.log(`⚠️ 表已存在，跳过创建`);
              continue;
            }
            throw error;
          }
        }
      }

      console.log('✅ 数据库初始化完成！');

      // 插入一些初始数据
      await insertInitialData(connection);

    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    process.exit(1);
  }
}

/**
 * 插入初始数据
 */
async function insertInitialData(connection) {
  try {
    console.log('📝 插入初始数据...');

    // 检查是否已有数据
    const [userRows] = await connection.execute('SELECT COUNT(*) as count FROM users');
    if (userRows[0].count > 0) {
      console.log('⚠️ 数据库已有数据，跳过初始数据插入');
      return;
    }

    // 插入测试用户
    await connection.execute(`
      INSERT INTO users (openid, nickname, avatar_url, status, publishing_credits) 
      VALUES 
      ('test_openid_1', '测试用户1', 'https://example.com/avatar1.jpg', 'active', 5),
      ('test_openid_2', '测试用户2', 'https://example.com/avatar2.jpg', 'inactive', 0)
    `);

    // 插入测试套餐
    await connection.execute(`
      INSERT INTO packages (title, description, price, credits_amount, package_type, is_active, sort_order)
      VALUES 
      ('新手体验包', '新用户专享，1条发布机会', 0.00, 1, 'free', 1, 1),
      ('基础套餐', '适合个人用户，5条发布机会', 9.90, 5, 'paid', 1, 2),
      ('标准套餐', '适合小企业，20条发布机会', 29.90, 20, 'paid', 1, 3),
      ('专业套餐', '适合中大企业，50条发布机会', 59.90, 50, 'paid', 1, 4)
    `);

    // 插入测试挂牌信息
    await connection.execute(`
      INSERT INTO listings (
        user_id, listing_type, company_name, status, price, is_negotiable,
        registration_province, registration_city, establishment_date,
        registered_capital_range, paid_in_status, company_type, tax_status,
        bank_account_status, has_trademark, has_patent, has_software_copyright,
        has_license_plate, has_social_security, shareholder_background,
        has_bidding_history, description
      ) VALUES 
      (1, '公司', '北京科技有限公司', '在售', 50000.00, 1, '北京市', '朝阳区', '2020-01-15', '100-500万', '已实缴', '普通公司', '一般纳税人', '已开户', 1, 0, 1, 0, 1, '自然人', 1, '优质科技公司，业务稳定，适合接手经营'),
      (1, '个体户', '张三餐饮店', '在售', 8000.00, 0, '上海市', '浦东新区', '2021-06-20', '10万以下', '已实缴', '不确定', '小规模', '未开户', 0, 0, 0, 0, 0, '自然人', 0, '位置优越的餐饮个体户，客流量稳定')
    `);

    console.log('✅ 初始数据插入完成！');

  } catch (error) {
    console.error('❌ 插入初始数据失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase()
    .then(() => {
      console.log('🎉 数据库初始化成功完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 数据库初始化失败:', error);
      process.exit(1);
    });
}

module.exports = { initDatabase };
